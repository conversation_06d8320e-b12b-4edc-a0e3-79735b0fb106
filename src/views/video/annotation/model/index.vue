<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
      <el-button type="primary" size="small" @click="showOperatorTypes">
        查看算子类型
      </el-button>
    </template>

    <!-- 算子组合列自定义渲染 -->
    <template #table:value4:simple="{ row }">
      <el-tag :type="getCombinationTagType(row.value4)">
        {{ row.value4 }}
      </el-tag>
    </template>

    <!-- 状态列自定义渲染 -->
    <template #table:value9:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value9)">
        {{ row.value9 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <template v-if="row.value9 === '启用'">
        <el-button type="text" size="mini" @click="handleEdit(row)">
          <i class="el-icon-edit"></i>
        </el-button>
        <el-button type="text" size="mini" @click="handleCopy(row)">
          <i class="el-icon-document-copy"></i>
        </el-button>
        <el-button type="text" size="mini" @click="handleDelete(row)">
          <i class="el-icon-delete"></i>
        </el-button>
      </template>
      <template v-else>
        <el-button type="text" size="mini" @click="handleEdit(row)">
          <i class="el-icon-edit"></i>
        </el-button>
        <el-button type="text" size="mini" @click="handleEnable(row)">
          <i class="el-icon-check"></i>
        </el-button>
        <el-button type="text" size="mini" @click="handleDelete(row)">
          <i class="el-icon-delete"></i>
        </el-button>
      </template>
    </template>

    <!-- 算子类型下拉列表 -->
    <template #after>
      <div v-if="showOperatorList" class="operator-list-container">
        <el-card class="operator-card">
          <div slot="header">
            <span>算子类型列表</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="showOperatorList = false">
              <i class="el-icon-close"></i>
            </el-button>
          </div>
          <div class="operator-list">
            <el-tag
              v-for="operator in operatorTypes"
              :key="operator.value"
              class="operator-tag"
              type="info"
            >
              {{ operator.label }}
            </el-tag>
          </div>
        </el-card>
      </div>
    </template>

    <template #info:before></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { operatorCombinationType, operatorChainStatus, operatorTypes } from '@/dicts/video/index.js'

export default {
  name: 'OperatorChainModel',
  data() {
    return {
      tableType: 'model_operator_orchestration',
      showOperatorList: false,
      operatorTypes
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '算子链模型',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 模型名称
          value1: {
            type: 'text',
            label: '模型名称',
            align: 'left',
            width: 200,
            search: {
              hidden: true
            }
          },
          // 算子链ID
          value2: {
            type: 'text',
            label: '算子链ID',
            align: 'left',
            width: 150,
            search: {
              placeholder: '请输入算子链ID'
            }
          },
          // 类型
          value3: {
            type: 'select',
            label: '算子组合',
            width: 150,
            search: {
              hidden: true
            },
            fieldProps: {
              multiple: true
            },
            options: operatorTypes
          },
          // 算子组合（串/并联）
          value4: {
            type: 'select',
            label: '类型',
            width: 120,
            search: {
              hidden: true
            },
            options: operatorCombinationType
          },
          // 功能描述
          value5: {
            type: 'text',
            label: '功能描述',
            width: 250,
            search: {
              placeholder: '请输入功能描述关键词'
            },
            form: {
              type: 'textarea'
            }
          },
          // 创建人
          value6: {
            type: 'text',
            label: '创建人',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 创建时间
          value7: {
            type: 'datetime',
            label: '创建时间',
            width: 160,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          // 状态
          value8: {
            type: 'select',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...operatorChainStatus
              ]
            },
            options: operatorChainStatus
          }
        }
      }
    }
  },
  methods: {
    // 获取算子组合标签类型
    getCombinationTagType(combination) {
      const typeMap = {
        '串联': 'primary',
        '并联': 'success'
      }
      return typeMap[combination] || 'info'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '启用': 'success',
        '禁用': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 编辑操作
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },

    // 复制操作
    handleCopy(row) {
      this.$modal.confirm(`确认要复制算子链"${row.value2}"吗？`).then(() => {
        // 这里可以调用复制接口
        console.log('复制算子链:', row)
        this.$modal.msgSuccess('复制成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 删除操作
    handleDelete(row) {
      this.$modal.confirm(`确认要删除算子链"${row.value2}"吗？`).then(() => {
        // 这里可以调用删除接口
        console.log('删除算子链:', row)
        this.$modal.msgSuccess('删除成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 启用操作
    handleEnable(row) {
      this.$modal.confirm(`确认要启用算子链"${row.value2}"吗？`).then(() => {
        // 这里可以调用启用接口
        console.log('启用算子链:', row)
        this.$modal.msgSuccess('启用成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 显示算子类型列表
    showOperatorTypes() {
      this.showOperatorList = true
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}

.operator-list-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 600px;
}

.operator-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.operator-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.operator-tag {
  margin: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.operator-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
