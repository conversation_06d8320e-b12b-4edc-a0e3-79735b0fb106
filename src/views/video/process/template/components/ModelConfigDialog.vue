<template>
  <el-dialog
    title="算法参数详情"
    :visible.sync="visible"
    width="600px"
    append-to-body
    @closed="onClosed"
  >
    <div v-if="modelData" class="model-config-container">
      <!-- 模型基本信息 -->
      <div class="model-info">
        <h4 class="info-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">算法名称：</span>
            <span class="info-value">{{ modelData.name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">算法编码：</span>
            <span class="info-value">{{ modelData.code }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">算法类型：</span>
            <span class="info-value">{{ getTypeLabel(modelData.type) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">版本号：</span>
            <span class="info-value">{{ modelData.version || '1.0.0' }}</span>
          </div>
        </div>
      </div>

      <!-- 输入参数 -->
      <div class="config-section">
        <h4 class="section-title">输入参数</h4>
        <div class="config-content">
          <pre class="config-json">{{ formatJson(inputConfig) }}</pre>
        </div>
      </div>

      <!-- 输出参数 -->
      <div class="config-section">
        <h4 class="section-title">输出参数</h4>
        <div class="config-content">
          <pre class="config-json">{{ formatJson(outputConfig) }}</pre>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { algorithmType } from '@/dicts/video/index.js'

export default {
  name: 'ModelConfigDialog',
  data() {
    return {
      visible: false,
      modelData: null,
      algorithmTypeDict: algorithmType
    }
  },
  computed: {
    // 模拟输入参数配置
    inputConfig() {
      if (!this.modelData) return {}

      const baseConfig = {
        url: 'http://*************/file/M00111.mp4',
        frontType: 'mp4',
        fileType: 'avi'
      }

      // 根据算法类型生成不同的配置
      switch (this.modelData.type) {
        case 'encode_convert':
          return {
            ...baseConfig,
            bitrate: '1024',
            resolution: '1920x1080',
            frameRate: '30'
          }
        case 'data_preprocess':
          return {
            ...baseConfig,
            noiseLevel: 'medium',
            brightness: '0.8',
            contrast: '1.2'
          }
        case 'deep_process':
          return {
            ...baseConfig,
            modelPath: '/models/detection.pth',
            threshold: '0.5',
            maxObjects: '10'
          }
        default:
          return baseConfig
      }
    },

    // 模拟输出参数配置
    outputConfig() {
      if (!this.modelData) return {}

      const baseOutput = {
        code: 200,
        message: '成功',
        data: {}
      }

      // 根据算法类型生成不同的输出配置
      switch (this.modelData.type) {
        case 'encode_convert':
          return {
            ...baseOutput,
            data: {
              taskId: '20250107234746434',
              outputUrl: 'http://*************/file/output.mp4',
              fileSize: '256MB',
              duration: '00:05:30'
            }
          }
        case 'data_preprocess':
          return {
            ...baseOutput,
            data: {
              taskId: '20250107234746434',
              processedUrl: 'http://*************/file/processed.mp4',
              quality: 'enhanced',
              noiseReduction: '85%'
            }
          }
        case 'deep_process':
          return {
            ...baseOutput,
            data: {
              taskId: '20250107234746434',
              detections: [
                { class: 'person', confidence: 0.95, bbox: [100, 100, 200, 300] },
                { class: 'car', confidence: 0.87, bbox: [300, 150, 450, 250] }
              ],
              totalObjects: 2
            }
          }
        default:
          return baseOutput
      }
    }
  },
  methods: {
    open(modelData) {
      this.visible = true
      this.modelData = modelData
    },

    close() {
      this.visible = false
    },

    onClosed() {
      this.modelData = null
    },

    // 获取算法类型标签
    getTypeLabel(type) {
      const typeItem = this.algorithmTypeDict.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },

    // 格式化JSON显示
    formatJson(obj) {
      return JSON.stringify(obj, null, 2)
    }
  }
}
</script>

<style lang="scss" scoped>
.model-config-container {
  .model-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .info-title {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;

      .info-item {
        display: flex;
        align-items: center;

        .info-label {
          font-size: 13px;
          color: #606266;
          white-space: nowrap;
        }

        .info-value {
          font-size: 13px;
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }

  .config-section {
    margin-bottom: 20px;

    .section-title {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .config-content {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      background: #fafafa;

      .config-json {
        margin: 0;
        padding: 12px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #2c3e50;
        background: transparent;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
